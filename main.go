package main

import (
	"context"
	"log"
	"os"

	"fin_mcp_server/internal/db"
	"fin_mcp_server/internal/mcp"
)

func getEnvWithFallback(primary, fallback, defaultValue string) string {
	if value := os.Getenv(primary); value != "" {
		return value
	}
	if value := os.Getenv(fallback); value != "" {
		return value
	}
	return defaultValue
}

func main() {
	// Support both new POSTGRES_* and legacy PG* environment variables
	host := getEnvWithFallback("POSTGRES_HOST", "PGHOST", "localhost")
	port := getEnvWithFallback("POSTGRES_PORT", "PGPORT", "5432")
	user := getEnvWithFallback("POSTGRES_USER", "PGUSER", "postgres")
	password := getEnvWithFallback("POSTGRES_PASSWORD", "PGPASSWORD", "postgres")
	dbname := getEnvWithFallback("POSTGRES_DB", "PGDATABASE", "postgres")

	log.Printf("Connecting to PostgreSQL: %s@%s:%s/%s", user, host, port, dbname)

	pg, err := db.NewPostgres(host, port, user, password, dbname)
	if err != nil {
		log.Fatal("DB connect failed:", err)
	}

	server := &mcp.MCPServer{DB: pg.DB}
	if err := server.Start(context.Background()); err != nil {
		log.Fatal("server error:", err)
	}
}
