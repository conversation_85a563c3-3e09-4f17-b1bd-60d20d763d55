#!/bin/bash

# Script untuk setup dan men<PERSON><PERSON>an MCP Server dengan PM2

echo "=== PostgreSQL MCP Server PM2 Setup ==="
echo ""

# Check if PM2 is installed
if ! command -v pm2 &> /dev/null; then
    echo "PM2 is not installed. Installing PM2..."
    npm install -g pm2
    if [ $? -ne 0 ]; then
        echo "Failed to install PM2. Please install it manually:"
        echo "npm install -g pm2"
        exit 1
    fi
fi

# Load environment variables
if [ -f .env ]; then
    echo "Loading environment variables from .env file..."
    export $(cat .env | grep -v '^#' | grep -v '^$' | xargs)
    echo "Environment variables loaded!"
else
    echo "Warning: .env file not found!"
    echo "Please create .env file with required configuration."
    exit 1
fi

# Create logs directory
echo "Creating logs directory..."
mkdir -p logs

# Build the application
echo "Building application..."
go build -o fin_mcp_server main.go
if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo "Build successful!"
echo ""

# Display configuration
echo "Current Configuration:"
echo "  MCP_TRANSPORT: $MCP_TRANSPORT"
echo "  MCP_PORT: $MCP_PORT"
echo "  POSTGRES_HOST: $POSTGRES_HOST"
echo "  POSTGRES_PORT: $POSTGRES_PORT"
echo "  POSTGRES_USER: $POSTGRES_USER"
echo "  POSTGRES_DB: $POSTGRES_DB"
echo ""

# Test database connection
echo "Testing database connection..."
./fin_mcp_server &
SERVER_PID=$!
sleep 2
kill $SERVER_PID 2>/dev/null
echo "Database connection test completed."
echo ""

# Start PM2 services
echo "Starting PM2 services..."
pm2 start ecosystem.config.js

echo ""
echo "=== PM2 Setup Complete ==="
echo ""
echo "Available commands:"
echo "  pm2 status          - Show service status"
echo "  pm2 logs            - Show logs"
echo "  pm2 restart all     - Restart all services"
echo "  pm2 stop all        - Stop all services"
echo "  pm2 delete all      - Delete all services"
echo "  pm2 monit           - Real-time monitoring"
echo ""
echo "Server endpoints:"
echo "  HTTP: http://localhost:8080/mcp"
echo "  SSE:  http://localhost:8081"
echo "  Health: http://localhost:8080/health"
echo ""
