#!/bin/bash

# Script untuk load environment variables dari .env file

if [ -f .env ]; then
    echo "Loading environment variables from .env file..."
    export $(cat .env | grep -v '^#' | grep -v '^$' | xargs)
    echo "Environment variables loaded successfully!"
else
    echo "Warning: .env file not found!"
    exit 1
fi

# Display loaded configuration
echo ""
echo "Current Configuration:"
echo "  MCP_TRANSPORT: $MCP_TRANSPORT"
echo "  MCP_PORT: $MCP_PORT"
echo "  POSTGRES_HOST: $POSTGRES_HOST"
echo "  POSTGRES_PORT: $POSTGRES_PORT"
echo "  POSTGRES_USER: $POSTGRES_USER"
echo "  POSTGRES_DB: $POSTGRES_DB"
echo ""
