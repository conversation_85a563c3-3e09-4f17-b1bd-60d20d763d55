#!/bin/bash

# Simple script to start MCP Server with PM2

echo "Starting PostgreSQL MCP Server with PM2..."

# Load environment variables
if [ -f .env ]; then
    echo "Loading environment variables from .env file..."
    export $(cat .env | grep -v '^#' | grep -v '^$' | xargs)
else
    echo "Warning: .env file not found!"
    exit 1
fi

# Build application
echo "Building application..."
go build -o fin_mcp_server main.go
if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

# Create logs directory
mkdir -p logs

# Start PM2 services
echo "Starting PM2 services..."
pm2 start ecosystem.config.js

echo ""
echo "PM2 services started successfully!"
echo ""
echo "Available commands:"
echo "  pm2 status    - Show service status"
echo "  pm2 logs      - Show logs"
echo "  pm2 stop all  - Stop all services"
echo "  pm2 restart all - Restart all services"
echo ""
echo "Server endpoints:"
echo "  HTTP: http://localhost:8080/mcp"
echo "  SSE:  http://localhost:8081"
echo "  Health: http://localhost:8080/health"
echo ""
